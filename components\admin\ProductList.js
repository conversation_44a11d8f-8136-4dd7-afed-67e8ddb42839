import { useState, useEffect, useCallback } from 'react';
import { getAuthToken } from '@/lib/auth-token-manager';
import styles from '@/styles/admin/ProductList.module.css';

export default function ProductList({ refreshKey = 0, onSelectProduct, onAdjustStock }) {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [filters, setFilters] = useState({
    category: '',
    stock_status: '',
  });

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Fetch products when search, sort, filters, or refreshKey changes
  const fetchProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        sort_by: sortBy,
        sort_order: sortOrder,
      });

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch);
      }

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value);
        }
      });

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch(`/api/admin/inventory/products?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token || ''}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const data = await response.json();

      // Apply EXACT working pattern from /services page - convert ALL data to primitive strings
      // This is the proven method that prevents React Error #130
      const safeProducts = (data.products || []).map(product => {
        // Convert every single field to a string, exactly like the working /services page
        return {
          id: String(product.id || ''),
          name: String(product.name || ''),
          description: String(product.description || ''),
          price: String(product.price || ''),
          cost_price: String(product.cost_price || ''),
          category: String(product.category || ''),
          status: String(product.status || 'active'),
          image_url: String(product.image_url || ''),
          stock: String(product.stock || '0'), // Convert number to string
          low_stock_threshold: String(product.low_stock_threshold || '0'), // Convert number to string
          sku: String(product.sku || ''),
          created_at: String(product.created_at || ''),
          updated_at: String(product.updated_at || '')
        };
      });

      setProducts(safeProducts);
    } catch (error) {
      console.error('Error fetching products:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [sortBy, sortOrder, debouncedSearch, filters]);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts, refreshKey]);

  // Handle sort column click
  const handleSort = (column) => {
    if (sortBy === column) {
      // Toggle sort order if clicking the same column
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Default to ascending order for new column
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Render sort indicator
  const renderSortIndicator = (column) => {
    if (sortBy !== column) return null;

    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? ' ↑' : ' ↓'}
      </span>
    );
  };

  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Currency formatting removed - not used in this component

  return (
    <div className={styles.productListContainer}>
      <div className={styles.filters}>
        <div className={styles.searchBox}>
          <input
            type="text"
            placeholder="Search products..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.filterControls}>
          <div className={styles.filterItem}>
            <label>Category:</label>
            <select
              name="category"
              value={filters.category}
              onChange={handleFilterChange}
            >
              <option value="">All Categories</option>
              <option value="face-painting">Face Painting</option>
              <option value="glitter">Glitter</option>
              <option value="accessories">Accessories</option>
              <option value="merchandise">Merchandise</option>
            </select>
          </div>

          <div className={styles.filterItem}>
            <label>Stock Status:</label>
            <select
              name="stock_status"
              value={filters.stock_status}
              onChange={handleFilterChange}
            >
              <option value="">All Stock</option>
              <option value="in_stock">In Stock</option>
              <option value="low_stock">Low Stock</option>
              <option value="out_of_stock">Out of Stock</option>
            </select>
          </div>
        </div>
      </div>

      {loading && (
        <div className={styles.loadingSpinner}>Loading products...</div>
      )}

      {error && (
        <div className={styles.errorMessage}>
          Error: {error}
          <button onClick={fetchProducts}>Try Again</button>
        </div>
      )}

      {!loading && !error && products.length === 0 && (
        <div className={styles.noResults}>
          No products found. Try adjusting your search or filters.
        </div>
      )}

      {!loading && !error && products.length > 0 && (
        <div className={styles.tableContainer}>
          <table className={styles.productTable}>
            <thead>
              <tr>
                <th>Image</th>
                <th onClick={() => handleSort('name')}>
                  Product Name {renderSortIndicator('name')}
                </th>
                <th onClick={() => handleSort('price')}>
                  Price {renderSortIndicator('price')}
                </th>
                <th onClick={() => handleSort('stock')}>
                  Stock {renderSortIndicator('stock')}
                </th>
                <th onClick={() => handleSort('category')}>
                  Category {renderSortIndicator('category')}
                </th>
                <th onClick={() => handleSort('status')}>
                  Status {renderSortIndicator('status')}
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {products.map((product) => {
                try {
                  return (
                    <tr key={product.id || Math.random()}>
                      <td className={styles.imageCell}>
                        {String(product.image_url || '') ? (
                          <img
                            src={String(product.image_url || '')}
                            alt={String(product.name || 'Product')}
                            width="50"
                            height="50"
                            style={{ objectFit: 'cover' }}
                            onError={(e) => {
                              // Prevent infinite error loops
                              if (e.target.src !== '/images/placeholder.svg') {
                                e.target.onerror = null;
                                e.target.src = '/images/placeholder.svg';
                              } else {
                                // If placeholder also fails, hide the image and show CSS fallback
                                e.target.style.display = 'none';
                                e.target.nextElementSibling.style.display = 'flex';
                              }
                            }}
                          />
                        ) : (
                          <div className={styles.noImage}>No Image</div>
                        )}
                        {/* CSS-based fallback for when both image and placeholder fail */}
                        <div
                          className={styles.imageFallback}
                          style={{ display: 'none' }}
                        >
                          📦
                        </div>
                      </td>
                      <td>{String(product.name || '')}</td>
                      <td>{String(product.price || '')}</td>
                      <td>
                        <span
                          className={`${styles.stockBadge} ${
                            parseInt(String(product.stock || '0'), 10) <= 0
                              ? styles.outOfStock
                              : parseInt(String(product.stock || '0'), 10) < 10
                              ? styles.lowStock
                              : styles.inStock
                          }`}
                        >
                          {String(product.stock || '0')}
                        </span>
                      </td>
                      <td>{String(product.category || '')}</td>
                      <td>
                        <span
                          className={`${styles.statusBadge} ${
                            String(product.status || '') === 'active'
                              ? styles.statusActive
                              : styles.statusInactive
                          }`}
                        >
                          {String(product.status || '')}
                        </span>
                      </td>
                      <td className={styles.actions}>
                        <button
                          className={styles.editButton}
                          onClick={() => onSelectProduct(product)}
                        >
                          Edit
                        </button>
                        {onAdjustStock && (
                          <button
                            className={styles.stockButton}
                            onClick={() => onAdjustStock(product)}
                          >
                            Adjust Stock
                          </button>
                        )}
                      </td>
                    </tr>
                  );
                } catch (error) {
                  console.error('Error rendering product row:', error, 'Product:', product);
                  return (
                    <tr key={product.id || Math.random()}>
                      <td colSpan="7" style={{ color: 'red', padding: '10px' }}>
                        Error displaying product data. Please refresh the page.
                      </td>
                    </tr>
                  );
                }
              })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
