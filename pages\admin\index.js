import { useState, useEffect, useCallback } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import BookingReminders from '@/components/admin/BookingReminders'
import DevAuthToggle from '@/components/admin/DevAuthToggle'
import { safeRender } from '@/lib/safe-render-utils'
import supabase from '@/lib/supabase'
import styles from '@/styles/admin/Dashboard.module.css'

export default function AdminDashboard() {
  const [summary, setSummary] = useState({
    totalBookings: 0,
    pendingBookings: 0,
    totalCustomers: 0,
    totalRevenue: 0,
    lowStockItems: 0
  })
  const [recentBookings, setRecentBookings] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [refreshKey, setRefreshKey] = useState(0)
  const [lastRefreshed, setLastRefreshed] = useState(new Date())

  // Function to manually refresh dashboard data
  const refreshDashboard = useCallback(() => {
    setRefreshKey(prevKey => prevKey + 1)
    setLastRefreshed(new Date())
  }, [])

  // Fetch dashboard summary data using Supabase directly
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get total bookings
        let totalBookings = 0;
        try {
          const { count, error } = await supabase
            .from('bookings')
            .select('*', { count: 'exact', head: true });

          if (!error) {
            totalBookings = count;
          } else {
            console.warn('Error fetching total bookings:', error);
          }
        } catch (err) {
          console.warn('Exception fetching total bookings:', err);
        }

        // Get pending bookings
        let pendingBookings = 0;
        try {
          const { count, error } = await supabase
            .from('bookings')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'pending');

          if (!error) {
            pendingBookings = count;
          } else {
            console.warn('Error fetching pending bookings:', error);
          }
        } catch (err) {
          console.warn('Exception fetching pending bookings:', err);
        }

        // Get total customers
        let totalCustomers = 0;
        try {
          const { count, error } = await supabase
            .from('customers')
            .select('*', { count: 'exact', head: true });

          if (!error) {
            totalCustomers = count;
          } else {
            console.warn('Error fetching total customers:', error);
          }
        } catch (err) {
          console.warn('Exception fetching total customers:', err);
        }

        // Get total revenue
        let totalRevenue = 0;
        try {
          const { data, error } = await supabase
            .from('payments')
            .select('amount')
            .eq('payment_status', 'completed');

          if (!error && data) {
            totalRevenue = data.reduce((sum, payment) => sum + (parseFloat(payment.amount) || 0), 0);
          } else {
            console.warn('Error fetching payments:', error);
          }
        } catch (err) {
          console.warn('Exception fetching payments:', err);
        }

        // Get low stock items
        let lowStockItems = 0;
        try {
          const { count, error } = await supabase
            .from('products')
            .select('*', { count: 'exact', head: true })
            .gt('stock', 0)
            .lte('stock', 10);

          if (!error) {
            lowStockItems = count;
          } else {
            console.warn('Error fetching low stock items:', error);
          }
        } catch (err) {
          console.warn('Exception fetching low stock items:', err);
        }

        // Get recent bookings
        let recentBookingsData = [];
        try {
          const { data, error } = await supabase
            .from('bookings')
            .select(`
              id,
              start_time,
              end_time,
              status,
              customers:customer_id (name, email),
              services:service_id (name)
            `)
            .order('created_at', { ascending: false })
            .limit(5);

          if (!error && data) {
            recentBookingsData = data;
          } else {
            console.warn('Error fetching recent bookings:', error);
          }
        } catch (err) {
          console.warn('Exception fetching recent bookings:', err);
        }

        setSummary({
          totalBookings,
          pendingBookings,
          totalCustomers,
          totalRevenue,
          lowStockItems
        });

        setRecentBookings(recentBookingsData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError(error.message || 'Failed to load dashboard data');

        // Set default values to prevent UI issues
        setSummary({
          totalBookings: 0,
          pendingBookings: 0,
          totalCustomers: 0,
          totalRevenue: 0,
          lowStockItems: 0
        });

        setRecentBookings([]);
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [refreshKey])

  return (
    <ProtectedRoute>
      <AdminLayout title="Dashboard">
        <div className={styles.dashboard}>
          <div className={styles.dashboardHeader}>
            <div className={styles.dashboardTitle}>
              <h1>Dashboard</h1>
              <div className={styles.lastRefreshed}>
                Last updated: {lastRefreshed.toLocaleTimeString()}
              </div>
            </div>
            <div className={styles.dashboardActions}>
              <button
                className={styles.refreshButton}
                onClick={refreshDashboard}
                disabled={loading}
              >
                {loading ? 'Refreshing...' : 'Refresh Data'}
              </button>
            </div>
          </div>
          {loading ? (
            <div className={styles.loading}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading dashboard data...</p>
            </div>
          ) : error ? (
            <div className={styles.error}>
              <div className={styles.errorIcon}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
              </div>
              <h3>Error Loading Dashboard</h3>
              <p>{error}</p>
              <button
                className={styles.retryButton}
                onClick={() => window.location.reload()}
              >
                Retry
              </button>
            </div>
          ) : (
            <>
              <div className={styles.summaryCards}>
                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Total Bookings</h3>
                    <p className={styles.summaryValue}>{summary.totalBookings}</p>
                  </div>
                </div>

                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Pending Bookings</h3>
                    <p className={styles.summaryValue}>{summary.pendingBookings}</p>
                  </div>
                </div>

                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Total Customers</h3>
                    <p className={styles.summaryValue}>{summary.totalCustomers}</p>
                  </div>
                </div>

                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="1" x2="12" y2="23"></line>
                      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Total Revenue</h3>
                    <p className={styles.summaryValue}>${summary.totalRevenue.toFixed(2)}</p>
                  </div>
                </div>

                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                      <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                      <line x1="12" y1="22.08" x2="12" y2="12"></line>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Low Stock Items</h3>
                    <p className={styles.summaryValue}>{summary.lowStockItems}</p>
                  </div>
                </div>
              </div>

              <div className={styles.dashboardGrid}>
                <div className={styles.dashboardSection}>
                  <h2 className={styles.sectionTitle}>Recent Bookings</h2>

                  {recentBookings.length > 0 ? (
                    <div className={styles.recentBookings}>
                      <table className={styles.bookingsTable}>
                        <thead>
                          <tr>
                            <th>Customer</th>
                            <th>Service</th>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {recentBookings.map(booking => {
                            try {
                              return (
                                <tr key={booking.id}>
                                  <td>{safeRender(booking.customers?.name, 'Unknown')}</td>
                                  <td>{safeRender(booking.services?.name, 'Unknown')}</td>
                                  <td>{booking.start_time ? new Date(booking.start_time).toLocaleDateString() : 'N/A'}</td>
                                  <td>
                                    {booking.start_time && booking.end_time ? (
                                      <>
                                        {new Date(booking.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                        {' - '}
                                        {new Date(booking.end_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                      </>
                                    ) : 'N/A'}
                                  </td>
                                  <td>
                                    <span className={`${styles.status} ${styles[safeRender(booking.status, 'unknown')]}`}>
                                      {safeRender(booking.status, 'Unknown')}
                                    </span>
                                  </td>
                                </tr>
                              );
                            } catch (error) {
                              console.error('Error rendering booking row:', error, 'Booking:', booking);
                              return (
                                <tr key={booking.id || Math.random()}>
                                  <td colSpan="5" style={{ color: 'red', padding: '10px' }}>
                                    Error displaying booking data. Please refresh the page.
                                  </td>
                                </tr>
                              );
                            }
                          })}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className={styles.noData}>No recent bookings found</div>
                  )}
                </div>

                <div className={styles.dashboardSection}>
                  <BookingReminders />
                </div>
              </div>

              {/* Development Authentication Toggle - only visible in development mode */}
              {process.env.NODE_ENV === 'development' && (
                <DevAuthToggle />
              )}
            </>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
