import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import styles from '@/styles/BookingModal.module.css';
import Image from 'next/image';
import { toast } from 'react-toastify';
import { useCustomer } from '@/contexts/CustomerContext';
import { safeRender } from '@/lib/safe-render-utils';

const BookingModal = ({ service, onClose, isRequestBooking }) => {
  const [step, setStep] = useState(1);
  const [selectedOption, setSelectedOption] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    date: '',
    time: '',
    location: '',
    message: '',
    agreeToTerms: false,
    marketingConsent: false
  });
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const { customer, saveGuestCustomer } = useCustomer();

  // Pre-fill customer data if available
  useEffect(() => {
    if (customer && typeof customer === 'object') {
      setFormData(prevData => ({
        ...prevData,
        name: safeRender(customer.name, ''),
        email: safeRender(customer.email, ''),
        phone: safeRender(customer.phone, ''),
        marketingConsent: Boolean(customer.marketing_consent)
      }));
    }
  }, [customer]);

  // Define service options based on the service type
  const getServiceOptions = () => {
    // Use pricing tiers from service data if available
    if (service.pricingTiers && service.pricingTiers.length > 0) {
      return service.pricingTiers.map(tier => ({
        id: tier.id,
        name: tier.name,
        hours: tier.duration / 60, // Convert minutes to hours for display
        duration: tier.duration, // Keep original duration in minutes
        price: parseFloat(tier.price),
        label: `${tier.name} (${tier.duration} min) - A$${parseFloat(tier.price).toFixed(0)}`,
        description: tier.description,
        is_default: tier.is_default
      }));
    }

    // Fallback to legacy hardcoded options for backward compatibility
    switch(service.id) {
      case 'airbrush-face-body':
        return [
          { hours: 2, price: 350, label: '2 hours - A$350' },
          { hours: 3, price: 450, label: '3 hours - A$450' },
          { hours: 4, price: 550, label: '4 hours - A$550' },
          { hours: 5, price: 650, label: '5 hours - A$650' }
        ];
      case 'kids-party':
        return [
          { hours: 2, price: 320, label: '2 hours - A$320' },
          { hours: 3, price: 420, label: '3 hours - A$420' },
          { hours: 4, price: 520, label: '4 hours - A$520' }
        ];
      case 'uv-face-body':
        return [
          { hours: 2, price: 320, label: '2 hours - A$320' },
          { hours: 3, price: 420, label: '3 hours - A$420' },
          { hours: 4, price: 520, label: '4 hours - A$520' }
        ];
      case 'glitter-bar':
        return [
          { hours: 2, price: 320, label: '2 hours - A$320' },
          { hours: 3, price: 420, label: '3 hours - A$420' },
          { hours: 4, price: 520, label: '4 hours - A$520' }
        ];
      case 'braid-bar':
        return [
          { hours: 2, price: 300, label: '2 hours - A$300' },
          { hours: 3, price: 400, label: '3 hours - A$400' },
          { hours: 4, price: 500, label: '4 hours - A$500' }
        ];
      case 'airbrush-tattoos':
        return [
          { hours: 2, price: 350, label: '2 hours - A$350' },
          { hours: 3, price: 450, label: '3 hours - A$450' },
          { hours: 4, price: 550, label: '4 hours - A$550' }
        ];
      case 'body-art-photoshoot':
        return [
          { hours: 2, price: 320, label: '2 hours - A$320' },
          { hours: 3, price: 450, label: '3 hours - A$450' },
          { hours: 4, price: 560, label: '4 hours - A$560' },
          { hours: 5, price: 650, label: '5 hours - A$650' }
        ];
      case 'facepaint-makeup':
        return [
          { hours: 1, price: 60, label: '1 hour - A$60' },
          { hours: 1.5, price: 90, label: '1.5 hours - A$90' },
          { hours: 2, price: 120, label: '2 hours - A$120' }
        ];
      case 'hair-braiding-short':
      case 'hair-braiding':
        return [
          { hours: 0.5, price: 40, label: '30 minutes - A$40' },
          { hours: 1, price: 70, label: '1 hour - A$70' }
        ];
      case 'viking-festival-braids':
        return [
          { hours: 1, price: 130, label: '1 hour - A$130' },
          { hours: 1.5, price: 180, label: '1.5 hours - A$180' },
          { hours: 2, price: 230, label: '2 hours - A$230' }
        ];
      case 'barber-cut':
        return [
          { hours: 0.75, price: 20, label: '45 minutes - A$20' },
          { hours: 1, price: 30, label: '1 hour - A$30' }
        ];
      case 'travel-fee':
        return [
          { hours: 1, price: 25, label: 'Travel Fee - A$25' }
        ];
      case 'parking-fee':
        return [
          { hours: 0.08, price: 15, label: 'Parking Fee - A$15' }
        ];
      default:
        return [
          { hours: 2, price: 320, label: '2 hours - A$320' },
          { hours: 3, price: 420, label: '3 hours - A$420' },
          { hours: 4, price: 520, label: '4 hours - A$520' }
        ];
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name) newErrors.name = 'Name is required';
    if (!formData.email) newErrors.email = 'Email is required';
    if (!formData.phone) newErrors.phone = 'Phone number is required';
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.time) newErrors.time = 'Time is required';
    if (!formData.location) newErrors.location = 'Location is required';
    if (!formData.agreeToTerms) newErrors.agreeToTerms = 'You must agree to the terms';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (step === 1 && !selectedOption) {
      setErrors({ option: 'Please select an option' });
      return;
    }

    if (step === 2) {
      if (!validateForm()) return;
    }

    setStep(step + 1);
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Save or update customer data
      if (!customer) {
        try {
          // Create guest customer
          const customerData = {
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            marketingConsent: formData.marketingConsent,
            isGuest: true
          };

          const response = await fetch('/api/public/customers', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(customerData)
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to save customer information');
          }

          // Save to context
          saveGuestCustomer(result.customer);
        } catch (error) {
          console.error('Error saving customer data:', error);
          // Continue anyway since we have the form data for the booking
        }
      }

      // Prepare the data for submission with safe rendering
      const bookingData = {
        name: safeRender(formData.name, ''),
        email: safeRender(formData.email, ''),
        phone: safeRender(formData.phone, ''),
        date: safeRender(formData.date, ''),
        time: safeRender(formData.time, ''),
        location: safeRender(formData.location, ''),
        message: safeRender(formData.message, ''),
        customerId: customer?.id ? safeRender(customer.id, null) : null,
        marketingConsent: Boolean(formData.marketingConsent),
        service: {
          id: safeRender(service.id, ''),
          name: safeRender(service.name, '').replace(/^🎨 |^✨ |^💇 |^🎭 |^🚗 |^🅿️ |^📸 /, '').trim(),
          duration: safeRender(service.duration, ''),
          price: safeRender(service.price, '')
        },
        option: selectedOption ? {
          label: safeRender(selectedOption.label, ''),
          price: safeRender(selectedOption.price, ''),
          duration: safeRender(selectedOption.duration, '')
        } : null
      };

      console.log('Submitting booking data:', bookingData);

      // Send the data to the public API endpoint
      const response = await fetch('/api/public/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit booking');
      }

      // Set success message from API response
      setSuccessMessage(result.message || (isRequestBooking
        ? 'Your booking request has been submitted. We will contact you shortly to confirm.'
        : 'Your booking has been confirmed. A confirmation email has been sent to your email address.'));

      // Show success message
      setStep(4);
    } catch (error) {
      console.error('Error submitting booking:', error);
      setSubmitError(error.message || 'An error occurred while submitting your booking. Please try again.');
      toast.error(error.message || 'An error occurred while submitting your booking. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Close modal when clicking outside or pressing ESC
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (e.target.classList.contains(styles.modalOverlay)) {
        onClose();
      }
    };

    const handleEscKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscKey);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [onClose]);

  // Use React Portal to render the modal at the document root
  const [isBrowser, setIsBrowser] = useState(false);

  useEffect(() => {
    setIsBrowser(true);
  }, []);

  const modalContent = (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <button className={styles.closeButton} onClick={onClose}>×</button>

        {step === 1 && (
          <div className={styles.modalContent}>
            <div className={styles.modalHeader}>
              <div className={styles.rainbowText}>SELECT YOUR PREFERENCES</div>
            </div>

            <div className={styles.serviceInfo}>
              <h2>{safeRender(service.name)}</h2>
              {service.pricingTiers && service.pricingTiers.length > 0 ? (
                <p>Multiple pricing options available</p>
              ) : (
                <p>{safeRender(service.duration)} • {safeRender(service.price)}</p>
              )}
            </div>

            <div className={styles.optionsSection}>
              <h3>{service.pricingTiers && service.pricingTiers.length > 0 ? 'Select Pricing Option' : 'Hours'}</h3>
              <div className={styles.selectWrapper}>
                <select
                  className={styles.optionSelect}
                  value={selectedOption ? safeRender(selectedOption.label) : ''}
                  onChange={(e) => {
                    const option = getServiceOptions().find(opt => safeRender(opt.label) === e.target.value);
                    setSelectedOption(option);
                    setErrors({...errors, option: null});
                  }}
                >
                  <option value="">Choose an option</option>
                  {getServiceOptions().map((option, index) => (
                    <option key={index} value={safeRender(option.label)}>
                      {safeRender(option.label)}
                    </option>
                  ))}
                </select>
                <div className={styles.selectArrow}></div>
              </div>
              {errors.option && <p className={styles.errorText}>{errors.option}</p>}
            </div>

            <div className={styles.modalFooter}>
              <button
                className={styles.nextButton}
                onClick={handleNext}
                disabled={!selectedOption}
              >
                Next
              </button>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className={styles.modalContent}>
            <div className={styles.modalHeader}>
              <div className={styles.rainbowText}>ENTER YOUR DETAILS</div>
            </div>

            <form className={styles.bookingForm}>
              <div className={styles.formGroup}>
                <label htmlFor="name">Full Name *</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={errors.name ? styles.inputError : ''}
                />
                {errors.name && <p className={styles.errorText}>{errors.name}</p>}
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="email">Email *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={errors.email ? styles.inputError : ''}
                  />
                  {errors.email && <p className={styles.errorText}>{errors.email}</p>}
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="phone">Phone *</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className={errors.phone ? styles.inputError : ''}
                  />
                  {errors.phone && <p className={styles.errorText}>{errors.phone}</p>}
                </div>
              </div>

              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="date">Date *</label>
                  <input
                    type="date"
                    id="date"
                    name="date"
                    value={formData.date}
                    onChange={handleInputChange}
                    className={errors.date ? styles.inputError : ''}
                  />
                  {errors.date && <p className={styles.errorText}>{errors.date}</p>}
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="time">Time *</label>
                  <input
                    type="time"
                    id="time"
                    name="time"
                    value={formData.time}
                    onChange={handleInputChange}
                    className={errors.time ? styles.inputError : ''}
                  />
                  {errors.time && <p className={styles.errorText}>{errors.time}</p>}
                </div>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="location">Location *</label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  className={errors.location ? styles.inputError : ''}
                  placeholder="Enter the event location"
                />
                {errors.location && <p className={styles.errorText}>{errors.location}</p>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="message">Additional Information</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows="4"
                  placeholder="Tell us more about your event or any special requirements"
                ></textarea>
              </div>

              <div className={styles.formGroup}>
                <div className={styles.checkboxGroup}>
                  <input
                    type="checkbox"
                    id="marketingConsent"
                    name="marketingConsent"
                    checked={formData.marketingConsent}
                    onChange={handleInputChange}
                  />
                  <label htmlFor="marketingConsent">
                    Sign up for marketing emails to receive updates on new services, special offers, and events
                  </label>
                </div>
              </div>

              <div className={styles.formGroup}>
                <div className={styles.checkboxGroup}>
                  <input
                    type="checkbox"
                    id="agreeToTerms"
                    name="agreeToTerms"
                    checked={formData.agreeToTerms}
                    onChange={handleInputChange}
                  />
                  <label htmlFor="agreeToTerms">
                    I agree to the terms and conditions
                  </label>
                </div>
                {errors.agreeToTerms && <p className={styles.errorText}>{errors.agreeToTerms}</p>}
              </div>
            </form>

            <div className={styles.modalFooter}>
              <button className={styles.backButton} onClick={handleBack}>
                Back
              </button>
              <button className={styles.nextButton} onClick={handleNext}>
                Next
              </button>
            </div>
          </div>
        )}

        {step === 3 && (
          <div className={styles.modalContent}>
            <div className={styles.modalHeader}>
              <div className={styles.rainbowText}>CONFIRM YOUR BOOKING</div>
            </div>

            <div className={styles.confirmationDetails}>
              <h2>Booking Summary</h2>

              <div className={styles.summaryItem}>
                <span>Service:</span>
                <span>{safeRender(service.name)}</span>
              </div>

              <div className={styles.summaryItem}>
                <span>Option:</span>
                <span>{selectedOption ? safeRender(selectedOption.label) : 'N/A'}</span>
              </div>

              <div className={styles.summaryItem}>
                <span>Date & Time:</span>
                <span>{safeRender(formData.date)} at {safeRender(formData.time)}</span>
              </div>

              <div className={styles.summaryItem}>
                <span>Location:</span>
                <span>{safeRender(formData.location)}</span>
              </div>

              <div className={styles.summaryItem}>
                <span>Contact:</span>
                <span>{safeRender(formData.name)} ({safeRender(formData.email)})</span>
              </div>

              {formData.message && (
                <div className={styles.summaryItem}>
                  <span>Additional Info:</span>
                  <span>{safeRender(formData.message)}</span>
                </div>
              )}

              <div className={styles.bookingNote}>
                {isRequestBooking ? (
                  <p>
                    <strong>Note:</strong> This is a booking request. We'll review your request and contact you to confirm availability and details.
                  </p>
                ) : (
                  <p>
                    <strong>Note:</strong> A deposit may be required to secure your booking for certain services.
                  </p>
                )}
              </div>
            </div>

            <div className={styles.modalFooter}>
              <button
                className={styles.backButton}
                onClick={handleBack}
                disabled={isSubmitting}
              >
                Back
              </button>
              <button
                className={styles.submitButton}
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Submitting...' : isRequestBooking ? 'Submit Request' : 'Confirm Booking'}
              </button>
            </div>
            {submitError && (
              <div className={styles.errorMessage}>
                {submitError}
              </div>
            )}
          </div>
        )}

        {step === 4 && (
          <div className={styles.modalContent}>
            <div className={styles.successMessage}>
              <div className={styles.successIcon}>✓</div>
              <h2>{isRequestBooking ? 'Request Submitted!' : 'Booking Confirmed!'}</h2>
              <p>
                {successMessage || (isRequestBooking
                  ? 'Thank you for your booking request. We\'ll review your request and contact you shortly to confirm availability and details.'
                  : 'Thank you for your booking. We\'ve sent a confirmation email with all the details to your email address.')}
              </p>
              <button className={styles.closeSuccessButton} onClick={onClose}>
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // Return null on server-side rendering
  if (!isBrowser) {
    return null;
  }

  // Use createPortal to render the modal at the document body level
  return createPortal(
    modalContent,
    document.body
  );
};

export default BookingModal;
